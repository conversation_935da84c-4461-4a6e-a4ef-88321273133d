#!/usr/bin/env python3
"""
Test script for the optimized enhanced workflow
"""

import asyncio
import logging
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_optimized_workflow():
    """Test the optimized workflow with entity extraction improvements"""
    
    # Create workflow instance
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test input - use a simpler case for faster testing
    test_input = WorkflowInput(
        location="Singapore",
        interest="fintech"
    )
    
    logger.info(f"Testing optimized workflow with location: {test_input.location}, interest: {test_input.interest}")
    
    try:
        # Execute the workflow
        results = await workflow.execute(test_input)
        
        # Display results
        logger.info(f"Workflow completed successfully. Found {len(results)} entities:")
        
        for i, result in enumerate(results, 1):
            logger.info(f"\n--- Entity {i} ---")
            logger.info(f"Name: {result.entity}")
            logger.info(f"Link: {result.link}")
            logger.info(f"Domain: {result.domain}")
            logger.info(f"Subdomains: {len(result.subdomains)} found")
            if result.subdomains:
                logger.info(f"Subdomain list: {', '.join(result.subdomains[:5])}{'...' if len(result.subdomains) > 5 else ''}")
            logger.info(f"IP addresses: {len(result.subdomain_ips)} resolved")
            
            # Show verification results
            if result.location_verification:
                verification = result.location_verification
                logger.info(f"Location relevance: {verification.get('location_relevance', 'unknown')}")
                logger.info(f"Field relevance: {verification.get('field_relevance', 'unknown')}")
                logger.info(f"Overall classification: {verification.get('overall_classification', 'unknown')}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error during workflow execution: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []

if __name__ == "__main__":
    # Run the test
    results = asyncio.run(test_optimized_workflow())
    print(f"\nTest completed. Found {len(results)} entities.")
